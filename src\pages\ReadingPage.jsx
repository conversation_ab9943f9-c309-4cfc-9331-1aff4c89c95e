import React, { useEffect, useRef } from "react";
import { CourseStructure, MainContent, ToolsPanel } from "../components/layout";
import { useAudioStore } from "../stores";
import { useLocalStorage } from "../hooks";
import { STORAGE_KEYS, TEXT_SIZES } from "../utils/constants";

const ReadingPage = () => {
  // Read data from session storage
  const [readingData, setReadingData] = React.useState(null);
  
  useEffect(() => {
    const data = sessionStorage.getItem('readingPageData');
    if (data) {
      setReadingData(JSON.parse(data));
    } else {
      console.error('No reading data found in session storage');
      // Optionally redirect back or show an error message
    }
  }, []);
  
  // Destructure the data once it's loaded
  const { courseData, pdfData } = readingData || {};
  const audioRef = useRef(null);
  const [textSize, setTextSize] = useLocalStorage(STORAGE_KEYS.TEXT_SIZE, TEXT_SIZES.NORMAL);
  const [activeTopicId, setActiveTopicId] = React.useState("keph102");
  const [currentTopic, setCurrentTopic] = React.useState(pdfData);

  const initialize = useAudioStore((state) => state.initialize);
  const loadPagePlaylist = useAudioStore((state) => state.loadPagePlaylist);

  // Handle first user interaction for audio
  useEffect(() => {
    let hasInteracted = false;

    const handleFirstInteraction = () => {
      if (!hasInteracted && audioRef.current) {
        hasInteracted = true;
        // Initialize audio context on first user interaction
        audioRef.current.load();
        document.removeEventListener("click", handleFirstInteraction);
        document.removeEventListener("touchstart", handleFirstInteraction);
      }
    };

    document.addEventListener("click", handleFirstInteraction);
    document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  // Initialize audio store
  useEffect(() => {
    if (audioRef.current) {
      const cleanup = initialize(audioRef, pdfData.audioSources);
      return cleanup;
    }
  }, [initialize]);

  // Load audio playlist for the PDF
  useEffect(() => {
    loadPagePlaylist(currentTopic.id, currentTopic.audioSources);
  }, [loadPagePlaylist, currentTopic]);

  // Handle topic selection
  const handleTopicSelection = React.useCallback((topicId) => {
    setActiveTopicId(topicId);
    // Find the topic in courseData
    const selectedTopic = courseData.topics.find(topic => topic.id === topicId);
    if (selectedTopic) {
      setCurrentTopic(selectedTopic);
    }
  }, [courseData.topics]);

  // Handle navigation to specific page/topic (for bookmarks and annotations)
  const handleNavigateToPage = React.useCallback((topicId, pageNumber) => {
    console.log('Navigating to:', { topicId, pageNumber });

    // Find the topic in courseData
    const targetTopic = courseData.topics.find(topic => topic.id === topicId);
    if (targetTopic) {
      setActiveTopicId(topicId);
      setCurrentTopic(targetTopic);

      // If there's a specific page number, we could potentially scroll to it
      // For now, we'll just switch to the topic
      // TODO: Implement page-specific navigation if needed
    } else {
      console.warn('Topic not found:', topicId);
    }
  }, [courseData.topics]);

  return (
    <div className="app-container">
      <audio ref={audioRef} style={{ display: "none" }} />

      {/* Course Structure Panel (Left Side) */}
      <div className="course-structure-sidebar">
        <CourseStructure
          topics={courseData.topics}
          activeTopicId={activeTopicId}
          onSelectTopic={handleTopicSelection}
          courseData={courseData}
          onNavigateToPage={handleNavigateToPage}
        />
      </div>

      {/* Main Content Area (Center) */}
      <div className="main-content-panel">
        <MainContent
          topic={currentTopic}
          textSize={textSize}
        />
      </div>

      {/* Tools Panel (Right Side) */}
      <div className="tools-panel-sidebar">
        <ToolsPanel
          textSize={textSize}
          onTextSizeChange={setTextSize}
          currentTopic={currentTopic}
        />
      </div>
    </div>
  );
};

export default ReadingPage;
